'use client'

import { MessageSquare, Lightbulb, Code, BookOpen } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useChat } from '@/contexts/ChatContext'

export function WelcomeScreen() {
  const { createNewConversation } = useChat()

  const examples = [
    {
      icon: MessageSquare,
      title: "Explain quantum computing",
      subtitle: "in simple terms"
    },
    {
      icon: Lightbulb,
      title: "Got any creative ideas",
      subtitle: "for a 10 year old's birthday?"
    },
    {
      icon: Code,
      title: "How do I make an HTTP request",
      subtitle: "in Javascript?"
    },
    {
      icon: BookOpen,
      title: "Help me write a short story",
      subtitle: "about a magical forest"
    }
  ]

  const handleExampleClick = (example) => {
    const conversationId = createNewConversation()
    // You could auto-send the example message here if desired
  }

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="max-w-3xl w-full text-center">
        {/* Logo/Title */}
        <div className="mb-8">
          <div className="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <MessageSquare className="w-10 h-10 text-green-600 dark:text-green-400" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Welcome to ChatGPT
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg">
            How can I help you today?
          </p>
        </div>

        {/* Example prompts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          {examples.map((example, index) => {
            const Icon = example.icon
            return (
              <button
                key={index}
                onClick={() => handleExampleClick(example)}
                className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left group"
              >
                <div className="flex items-start gap-3">
                  <Icon className="w-5 h-5 text-gray-500 dark:text-gray-400 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors flex-shrink-0 mt-0.5" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                      {example.title}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {example.subtitle}
                    </div>
                  </div>
                </div>
              </button>
            )
          })}
        </div>

        {/* Capabilities */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
          <div className="space-y-2">
            <div className="font-medium text-gray-900 dark:text-gray-100">💡 Examples</div>
            <div className="text-gray-600 dark:text-gray-400 space-y-1">
              <div>"Explain quantum computing in simple terms"</div>
              <div>"Got any creative ideas for a 10 year old's birthday?"</div>
              <div>"How do I make an HTTP request in Javascript?"</div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="font-medium text-gray-900 dark:text-gray-100">⚡ Capabilities</div>
            <div className="text-gray-600 dark:text-gray-400 space-y-1">
              <div>Remembers what user said earlier in the conversation</div>
              <div>Allows user to provide follow-up corrections</div>
              <div>Trained to decline inappropriate requests</div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="font-medium text-gray-900 dark:text-gray-100">⚠️ Limitations</div>
            <div className="text-gray-600 dark:text-gray-400 space-y-1">
              <div>May occasionally generate incorrect information</div>
              <div>May occasionally produce harmful instructions or biased content</div>
              <div>Limited knowledge of world and events after 2021</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
