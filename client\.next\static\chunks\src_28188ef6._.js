(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": ()=>cn,
    "copyToClipboard": ()=>copyToClipboard,
    "formatTimestamp": ()=>formatTimestamp,
    "generateConversationTitle": ()=>generateConversationTitle,
    "generateId": ()=>generateId,
    "getStoredTheme": ()=>getStoredTheme,
    "getSystemTheme": ()=>getSystemTheme,
    "handleKeyboardShortcut": ()=>handleKeyboardShortcut,
    "isModifierKey": ()=>isModifierKey,
    "loadFromLocalStorage": ()=>loadFromLocalStorage,
    "saveToLocalStorage": ()=>saveToLocalStorage,
    "setTheme": ()=>setTheme,
    "sleep": ()=>sleep,
    "truncateText": ()=>truncateText,
    "typewriterEffect": ()=>typewriterEffect
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$3$2e$1$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/tailwind-merge@3.3.1/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn() {
    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){
        inputs[_key] = arguments[_key];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$3$2e$1$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const getSystemTheme = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    //TURBOPACK unreachable
    ;
};
const setTheme = (theme)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        const root = document.documentElement;
        if (theme === 'dark') {
            root.setAttribute('data-theme', 'dark');
        } else {
            root.removeAttribute('data-theme');
        }
        localStorage.setItem('theme', theme);
    }
};
const getStoredTheme = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem('theme') || 'system';
    }
    //TURBOPACK unreachable
    ;
};
const generateId = ()=>{
    return Math.random().toString(36).substring(2, 11);
};
const formatTimestamp = (timestamp)=>{
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    if (diffInHours < 1) {
        return 'Just now';
    } else if (diffInHours < 24) {
        return "".concat(Math.floor(diffInHours), "h ago");
    } else if (diffInHours < 168) {
        return "".concat(Math.floor(diffInHours / 24), "d ago");
    } else {
        return date.toLocaleDateString();
    }
};
const truncateText = function(text) {
    let maxLength = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
};
const generateConversationTitle = (firstMessage)=>{
    if (!firstMessage) return 'New Chat';
    // Remove markdown and clean up the text
    const cleanText = firstMessage.replace(/[#*`_~]/g, '') // Remove markdown characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
    return truncateText(cleanText, 40);
};
const saveToLocalStorage = (key, data)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
        }
    }
};
const loadFromLocalStorage = function(key) {
    let defaultValue = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;
    if ("TURBOPACK compile-time truthy", 1) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            return defaultValue;
        }
    }
    return defaultValue;
};
const isModifierKey = (event)=>{
    return event.ctrlKey || event.metaKey || event.altKey;
};
const handleKeyboardShortcut = (event, shortcuts)=>{
    const key = event.key.toLowerCase();
    const modifiers = {
        ctrl: event.ctrlKey,
        meta: event.metaKey,
        alt: event.altKey,
        shift: event.shiftKey
    };
    for (const shortcut of shortcuts){
        if (shortcut.key === key && shortcut.ctrl === modifiers.ctrl && shortcut.meta === modifiers.meta && shortcut.alt === modifiers.alt && shortcut.shift === modifiers.shift) {
            event.preventDefault();
            shortcut.action();
            return true;
        }
    }
    return false;
};
const copyToClipboard = async (text)=>{
    if ("object" !== 'undefined' && navigator.clipboard) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            return false;
        }
    }
    return false;
};
const sleep = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));
const typewriterEffect = async function(text, callback) {
    let speed = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30, abortSignal = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : null;
    for(let i = 0; i <= text.length; i++){
        // Check if operation was aborted
        if (abortSignal === null || abortSignal === void 0 ? void 0 : abortSignal.aborted) {
            throw new DOMException('Operation was aborted', 'AbortError');
        }
        callback(text.substring(0, i));
        await sleep(speed);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ChatContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChatProvider": ()=>ChatProvider,
    "useChat": ()=>useChat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const ChatContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])();
// Action types
const ACTIONS = {
    SET_CONVERSATIONS: 'SET_CONVERSATIONS',
    ADD_CONVERSATION: 'ADD_CONVERSATION',
    UPDATE_CONVERSATION: 'UPDATE_CONVERSATION',
    DELETE_CONVERSATION: 'DELETE_CONVERSATION',
    SET_CURRENT_CONVERSATION: 'SET_CURRENT_CONVERSATION',
    ADD_MESSAGE: 'ADD_MESSAGE',
    UPDATE_MESSAGE: 'UPDATE_MESSAGE',
    DELETE_MESSAGE: 'DELETE_MESSAGE',
    SET_IS_TYPING: 'SET_IS_TYPING',
    SET_SIDEBAR_OPEN: 'SET_SIDEBAR_OPEN',
    SET_GENERATION_CONTROLLER: 'SET_GENERATION_CONTROLLER',
    SET_CAN_REGENERATE: 'SET_CAN_REGENERATE'
};
// Initial state
const initialState = {
    conversations: [],
    currentConversationId: null,
    isTyping: false,
    sidebarOpen: true,
    generationController: null,
    canRegenerate: false // Whether the last message can be regenerated
};
// Reducer function
function chatReducer(state, action) {
    switch(action.type){
        case ACTIONS.SET_CONVERSATIONS:
            return {
                ...state,
                conversations: action.payload
            };
        case ACTIONS.ADD_CONVERSATION:
            return {
                ...state,
                conversations: [
                    action.payload,
                    ...state.conversations
                ],
                currentConversationId: action.payload.id
            };
        case ACTIONS.UPDATE_CONVERSATION:
            return {
                ...state,
                conversations: state.conversations.map((conv)=>conv.id === action.payload.id ? {
                        ...conv,
                        ...action.payload.updates
                    } : conv)
            };
        case ACTIONS.DELETE_CONVERSATION:
            const filteredConversations = state.conversations.filter((conv)=>conv.id !== action.payload);
            const newCurrentId = state.currentConversationId === action.payload ? filteredConversations.length > 0 ? filteredConversations[0].id : null : state.currentConversationId;
            return {
                ...state,
                conversations: filteredConversations,
                currentConversationId: newCurrentId
            };
        case ACTIONS.SET_CURRENT_CONVERSATION:
            return {
                ...state,
                currentConversationId: action.payload
            };
        case ACTIONS.ADD_MESSAGE:
            return {
                ...state,
                conversations: state.conversations.map((conv)=>conv.id === action.payload.conversationId ? {
                        ...conv,
                        messages: [
                            ...conv.messages,
                            action.payload.message
                        ],
                        updatedAt: new Date().toISOString()
                    } : conv)
            };
        case ACTIONS.UPDATE_MESSAGE:
            return {
                ...state,
                conversations: state.conversations.map((conv)=>conv.id === action.payload.conversationId ? {
                        ...conv,
                        messages: conv.messages.map((msg)=>msg.id === action.payload.messageId ? {
                                ...msg,
                                ...action.payload.updates
                            } : msg)
                    } : conv)
            };
        case ACTIONS.DELETE_MESSAGE:
            return {
                ...state,
                conversations: state.conversations.map((conv)=>conv.id === action.payload.conversationId ? {
                        ...conv,
                        messages: conv.messages.filter((msg)=>msg.id !== action.payload.messageId)
                    } : conv)
            };
        case ACTIONS.SET_IS_TYPING:
            return {
                ...state,
                isTyping: action.payload
            };
        case ACTIONS.SET_GENERATION_CONTROLLER:
            return {
                ...state,
                generationController: action.payload
            };
        case ACTIONS.SET_CAN_REGENERATE:
            return {
                ...state,
                canRegenerate: action.payload
            };
        case ACTIONS.SET_SIDEBAR_OPEN:
            return {
                ...state,
                sidebarOpen: action.payload
            };
        default:
            return state;
    }
}
function ChatProvider(param) {
    let { children } = param;
    _s();
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(chatReducer, initialState);
    // Load conversations from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatProvider.useEffect": ()=>{
            const savedConversations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadFromLocalStorage"])('chatgpt-conversations', []);
            const savedCurrentId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadFromLocalStorage"])('chatgpt-current-conversation', null);
            const savedSidebarState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadFromLocalStorage"])('chatgpt-sidebar-open', true);
            if (savedConversations.length > 0) {
                dispatch({
                    type: ACTIONS.SET_CONVERSATIONS,
                    payload: savedConversations
                });
            }
            if (savedCurrentId) {
                dispatch({
                    type: ACTIONS.SET_CURRENT_CONVERSATION,
                    payload: savedCurrentId
                });
            }
            dispatch({
                type: ACTIONS.SET_SIDEBAR_OPEN,
                payload: savedSidebarState
            });
        }
    }["ChatProvider.useEffect"], []);
    // Save to localStorage whenever conversations change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatProvider.useEffect": ()=>{
            if (state.conversations.length > 0) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["saveToLocalStorage"])('chatgpt-conversations', state.conversations);
            }
        }
    }["ChatProvider.useEffect"], [
        state.conversations
    ]);
    // Save current conversation ID
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatProvider.useEffect": ()=>{
            if (state.currentConversationId) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["saveToLocalStorage"])('chatgpt-current-conversation', state.currentConversationId);
            }
        }
    }["ChatProvider.useEffect"], [
        state.currentConversationId
    ]);
    // Save sidebar state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatProvider.useEffect": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["saveToLocalStorage"])('chatgpt-sidebar-open', state.sidebarOpen);
        }
    }["ChatProvider.useEffect"], [
        state.sidebarOpen
    ]);
    // Action creators
    const actions = {
        createNewConversation: ()=>{
            const newConversation = {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
                title: 'New Chat',
                messages: [],
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            dispatch({
                type: ACTIONS.ADD_CONVERSATION,
                payload: newConversation
            });
            return newConversation.id;
        },
        deleteConversation: (conversationId)=>{
            dispatch({
                type: ACTIONS.DELETE_CONVERSATION,
                payload: conversationId
            });
        },
        setCurrentConversation: (conversationId)=>{
            dispatch({
                type: ACTIONS.SET_CURRENT_CONVERSATION,
                payload: conversationId
            });
        },
        addMessage: (conversationId, message)=>{
            const messageWithId = {
                ...message,
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateId"])(),
                timestamp: new Date().toISOString()
            };
            dispatch({
                type: ACTIONS.ADD_MESSAGE,
                payload: {
                    conversationId,
                    message: messageWithId
                }
            });
            // Update conversation title if this is the first user message
            const conversation = state.conversations.find((c)=>c.id === conversationId);
            if (conversation && conversation.messages.length === 0 && message.role === 'user') {
                const title = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateConversationTitle"])(message.content);
                dispatch({
                    type: ACTIONS.UPDATE_CONVERSATION,
                    payload: {
                        id: conversationId,
                        updates: {
                            title
                        }
                    }
                });
            }
            return messageWithId.id;
        },
        updateMessage: (conversationId, messageId, updates)=>{
            dispatch({
                type: ACTIONS.UPDATE_MESSAGE,
                payload: {
                    conversationId,
                    messageId,
                    updates
                }
            });
        },
        deleteMessage: (conversationId, messageId)=>{
            dispatch({
                type: ACTIONS.DELETE_MESSAGE,
                payload: {
                    conversationId,
                    messageId
                }
            });
        },
        setIsTyping: (isTyping)=>{
            dispatch({
                type: ACTIONS.SET_IS_TYPING,
                payload: isTyping
            });
        },
        setGenerationController: (controller)=>{
            dispatch({
                type: ACTIONS.SET_GENERATION_CONTROLLER,
                payload: controller
            });
        },
        setCanRegenerate: (canRegenerate)=>{
            dispatch({
                type: ACTIONS.SET_CAN_REGENERATE,
                payload: canRegenerate
            });
        },
        stopGeneration: ()=>{
            if (state.generationController) {
                state.generationController.abort();
                dispatch({
                    type: ACTIONS.SET_GENERATION_CONTROLLER,
                    payload: null
                });
                dispatch({
                    type: ACTIONS.SET_IS_TYPING,
                    payload: false
                });
            }
        },
        toggleSidebar: ()=>{
            dispatch({
                type: ACTIONS.SET_SIDEBAR_OPEN,
                payload: !state.sidebarOpen
            });
        },
        setSidebarOpen: (open)=>{
            dispatch({
                type: ACTIONS.SET_SIDEBAR_OPEN,
                payload: open
            });
        }
    };
    const value = {
        ...state,
        ...actions,
        currentConversation: state.conversations.find((c)=>c.id === state.currentConversationId) || null
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ChatContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ChatContext.js",
        lineNumber: 286,
        columnNumber: 5
    }, this);
}
_s(ChatProvider, "4iRlf1j8yd8EOnLzdn/Ri/p3tw0=");
_c = ChatProvider;
function useChat() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ChatContext);
    if (!context) {
        throw new Error('useChat must be used within a ChatProvider');
    }
    return context;
}
_s1(useChat, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "ChatProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ThemeContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": ()=>ThemeProvider,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])();
function ThemeProvider(param) {
    let { children } = param;
    _s();
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('system');
    const [resolvedTheme, setResolvedTheme] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('light');
    // Initialize theme on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            const storedTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStoredTheme"])();
            setThemeState(storedTheme);
            const resolved = storedTheme === 'system' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSystemTheme"])() : storedTheme;
            setResolvedTheme(resolved);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setTheme"])(resolved);
        }
    }["ThemeProvider.useEffect"], []);
    // Listen for system theme changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (theme === 'system') {
                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                const handleChange = {
                    "ThemeProvider.useEffect.handleChange": (e)=>{
                        const newTheme = e.matches ? 'dark' : 'light';
                        setResolvedTheme(newTheme);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setTheme"])(newTheme);
                    }
                }["ThemeProvider.useEffect.handleChange"];
                mediaQuery.addEventListener('change', handleChange);
                return ({
                    "ThemeProvider.useEffect": ()=>mediaQuery.removeEventListener('change', handleChange)
                })["ThemeProvider.useEffect"];
            }
        }
    }["ThemeProvider.useEffect"], [
        theme
    ]);
    const setTheme = (newTheme)=>{
        setThemeState(newTheme);
        const resolved = newTheme === 'system' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSystemTheme"])() : newTheme;
        setResolvedTheme(resolved);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setTheme"])(resolved);
    };
    const toggleTheme = ()=>{
        const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';
        setTheme(newTheme);
    };
    const value = {
        theme,
        resolvedTheme,
        setTheme,
        toggleTheme,
        isDark: resolvedTheme === 'dark'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ThemeContext.js",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
_s(ThemeProvider, "3ta2GX6xuXN17yswPfWwwf3bI7w=");
_c = ThemeProvider;
function useTheme() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$2_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
_s1(useTheme, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_28188ef6._.js.map